import ccxt, { kraken, type Ticker } from "ccxt";

function handle(exchange: kraken, symbol: string, ticker: Ticker) {
  console.log(new Date(), exchange.id, symbol, ticker["last"]);
}

async function loopTicker(exchange: kraken, symbol: string) {
  while (true) {
    try {
      const ticker = await exchange.watchTicker(symbol);
      handle(exchange, symbol, ticker);
    } catch (e) {
      console.log(symbol, e);
      // do nothing and retry on next loop iteration
      // throw e // uncomment to break all loops in case of an error in any one of them
      // break // you can also break just this one loop if it fails
    }
  }
}

async function loopTrades(exchange: kraken, symbol: string) {
  while (true) {
    try {
      const trades = await exchange.watchTrades(symbol);
      console.log("Trades: ", trades);
    } catch (e) {
      console.log(symbol, e);
      // do nothing and retry on next loop iteration
      // throw e // uncomment to break all loops in case of an error in any one of them
      // break // you can also break just this one
    }
  }
}

async function loopCandles(exchange: kraken, symbol: string) {
  while (true) {
    try {
      const ohlcv = await exchange.watchOHLCV(symbol, "1h");
      console.log("OHLCV: ", ohlcv);
    } catch (e) {
      console.log(symbol, e);
      // do nothing and retry on next loop iteration
      // throw e // uncomment to break all loops in case of an error in any one of them
      // break // you can also break just this one loop if it fails
    }
  }
}

async function main() {
  const exchange = new ccxt.pro.kraken(); // usd(s)-margined contracts
  //
  // or
  //
  //  const exchange = new ccxt.pro.binance () // spot markets
  //
  // WARNING: when using the spot markets mind subscription limits!
  // don't attempt to subscribe to all of them
  // the exchanges will not allow that in general
  // instead, specify a shorter list of symbols to subscribe to
  //
  // or
  //
  //  const exchange = new ccxt.pro.binancecoinm () // coin-margined contracts

  if (exchange.has["watchTicker"]) {
    await exchange.loadMarkets();
    // many symbols
    await Promise.all(exchange.symbols.map((symbol) => console.log(symbol)));

    const symbol = "BTC/USD";

    loopTicker(exchange, symbol);
    loopTrades(exchange, symbol);
    loopCandles(exchange, symbol);
    //
    // or
    //
    // const symbols = [ 'BTC/USDT', 'ETH/USDT' ] // specific symbols
    // await Promise.all (symbols.map (symbol => loop (exchange, symbol)))
    //
    // or
    //
    // await loop (exchange, 'BTC/USDT') // one symbol
  } else {
    console.log(exchange.id, "does not support watchTicker yet");
  }
}

main();
