import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetActivate2FA(params?: {}): Promise<implicitReturnType>;
    publicGetAuthenticate2FA(params?: {}): Promise<implicitReturnType>;
    publicGetAuthenticateUser(params?: {}): Promise<implicitReturnType>;
    publicGetGetL2Snapshot(params?: {}): Promise<implicitReturnType>;
    publicGetGetLevel1(params?: {}): Promise<implicitReturnType>;
    publicGetGetValidate2FARequiredEndpoints(params?: {}): Promise<implicitReturnType>;
    publicGetLogOut(params?: {}): Promise<implicitReturnType>;
    publicGetGetTickerHistory(params?: {}): Promise<implicitReturnType>;
    publicGetGetProduct(params?: {}): Promise<implicitReturnType>;
    publicGetGetProducts(params?: {}): Promise<implicitReturnType>;
    publicGetGetInstrument(params?: {}): Promise<implicitReturnType>;
    publicGetGetInstruments(params?: {}): Promise<implicitReturnType>;
    publicGetPing(params?: {}): Promise<implicitReturnType>;
    publicGetTrades(params?: {}): Promise<implicitReturnType>;
    publicGetGetLastTrades(params?: {}): Promise<implicitReturnType>;
    publicGetSubscribeLevel1(params?: {}): Promise<implicitReturnType>;
    publicGetSubscribeLevel2(params?: {}): Promise<implicitReturnType>;
    publicGetSubscribeTicker(params?: {}): Promise<implicitReturnType>;
    publicGetSubscribeTrades(params?: {}): Promise<implicitReturnType>;
    publicGetSubscribeBlockTrades(params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeBlockTrades(params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeLevel1(params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeLevel2(params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeTicker(params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeTrades(params?: {}): Promise<implicitReturnType>;
    publicGetAuthenticate(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserAccountInfos(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserAccounts(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserAffiliateCount(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserAffiliateTag(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserConfig(params?: {}): Promise<implicitReturnType>;
    privateGetGetAllUnredactedUserConfigsForUser(params?: {}): Promise<implicitReturnType>;
    privateGetGetUnredactedUserConfigByKey(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserDevices(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserReportTickets(params?: {}): Promise<implicitReturnType>;
    privateGetGetUserReportWriterResultRecords(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountInfo(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountPositions(params?: {}): Promise<implicitReturnType>;
    privateGetGetAllAccountConfigs(params?: {}): Promise<implicitReturnType>;
    privateGetGetTreasuryProductsForAccount(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountTrades(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountTransactions(params?: {}): Promise<implicitReturnType>;
    privateGetGetOpenTradeReports(params?: {}): Promise<implicitReturnType>;
    privateGetGetAllOpenTradeReports(params?: {}): Promise<implicitReturnType>;
    privateGetGetTradesHistory(params?: {}): Promise<implicitReturnType>;
    privateGetGetOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateGetGetOpenQuotes(params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderFee(params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderHistory(params?: {}): Promise<implicitReturnType>;
    privateGetGetOrdersHistory(params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderStatus(params?: {}): Promise<implicitReturnType>;
    privateGetGetOmsFeeTiers(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountDepositTransactions(params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountWithdrawTransactions(params?: {}): Promise<implicitReturnType>;
    privateGetGetAllDepositRequestInfoTemplates(params?: {}): Promise<implicitReturnType>;
    privateGetGetDepositInfo(params?: {}): Promise<implicitReturnType>;
    privateGetGetDepositRequestInfoTemplate(params?: {}): Promise<implicitReturnType>;
    privateGetGetDeposits(params?: {}): Promise<implicitReturnType>;
    privateGetGetDepositTicket(params?: {}): Promise<implicitReturnType>;
    privateGetGetDepositTickets(params?: {}): Promise<implicitReturnType>;
    privateGetGetOMSWithdrawFees(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawFee(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdraws(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawTemplate(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawTemplateTypes(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawTicket(params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawTickets(params?: {}): Promise<implicitReturnType>;
    privatePostAddUserAffiliateTag(params?: {}): Promise<implicitReturnType>;
    privatePostCancelUserReport(params?: {}): Promise<implicitReturnType>;
    privatePostRegisterNewDevice(params?: {}): Promise<implicitReturnType>;
    privatePostSubscribeAccountEvents(params?: {}): Promise<implicitReturnType>;
    privatePostUpdateUserAffiliateTag(params?: {}): Promise<implicitReturnType>;
    privatePostGenerateTradeActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostGenerateTransactionActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostGenerateTreasuryActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostScheduleTradeActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostScheduleTransactionActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostScheduleTreasuryActivityReport(params?: {}): Promise<implicitReturnType>;
    privatePostCancelAllOrders(params?: {}): Promise<implicitReturnType>;
    privatePostCancelOrder(params?: {}): Promise<implicitReturnType>;
    privatePostCancelQuote(params?: {}): Promise<implicitReturnType>;
    privatePostCancelReplaceOrder(params?: {}): Promise<implicitReturnType>;
    privatePostCreateQuote(params?: {}): Promise<implicitReturnType>;
    privatePostModifyOrder(params?: {}): Promise<implicitReturnType>;
    privatePostSendOrder(params?: {}): Promise<implicitReturnType>;
    privatePostSubmitBlockTrade(params?: {}): Promise<implicitReturnType>;
    privatePostUpdateQuote(params?: {}): Promise<implicitReturnType>;
    privatePostCancelWithdraw(params?: {}): Promise<implicitReturnType>;
    privatePostCreateDepositTicket(params?: {}): Promise<implicitReturnType>;
    privatePostCreateWithdrawTicket(params?: {}): Promise<implicitReturnType>;
    privatePostSubmitDepositTicketComment(params?: {}): Promise<implicitReturnType>;
    privatePostSubmitWithdrawTicketComment(params?: {}): Promise<implicitReturnType>;
    privatePostGetOrderHistoryByOrderId(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
