import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetApiSpotV1PublicTime(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1PublicCurrencies(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketTicker(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketTickers(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketFills(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketCandles(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketDepth(params?: {}): Promise<implicitReturnType>;
    publicGetApiSpotV1MarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketContracts(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketDepth(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketTicker(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketTickers(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketFills(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketCandles(params?: {}): Promise<implicitReturnType>;
    publicGetPiMixV1MarketIndex(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketFundingTime(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketHistoryFundRate(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketCurrentFundRate(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketOpenInterest(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketSymbolLeverage(params?: {}): Promise<implicitReturnType>;
    publicGetApiMixV1MarketQueryPositionLever(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1WalletDepositAddress(params?: {}): Promise<implicitReturnType>;
    privateGetPiSpotV1WalletWithdrawalList(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1WalletWithdrawalListV2(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1WalletDepositList(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1AccountGetInfo(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1AccountAssets(params?: {}): Promise<implicitReturnType>;
    privateGetApiSpotV1AccountTransferRecords(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1AccountAccount(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1AccountAccounts(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1PositionSinglePositionV2(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1PositionAllPositionV2(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1AccountAccountBill(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1AccountAccountBusinessBill(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderCurrent(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderMarginCoinCurrent(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderHistory(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderHistoryProductType(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderDetail(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderFills(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1OrderAllFills(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1PlanCurrentPlan(params?: {}): Promise<implicitReturnType>;
    privateGetApiMixV1PlanHistoryPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1WalletTransferV2(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1WalletWithdrawalV2(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1WalletWithdrawalInnerV2(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1AccountBills(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeCancelOrder(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeCancelOrderV2(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeCancelSymbolOrder(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeCancelBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeCancelBatchOrdersV2(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeOrderInfo(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeOpenOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeHistory(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1TradeFills(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanPlacePlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanModifyPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanCancelPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanCurrentPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanHistoryPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiSpotV1PlanBatchCancelPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1AccountOpenCount(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1AccountSetLeverage(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1AccountSetMargin(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1AccountSetMarginMode(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1AccountSetPositionMode(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderCancelOrder(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderCancelBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderCancelSymbolOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1OrderCancelAllOrders(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanPlacePlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanModifyPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanModifyPlanPreset(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanPlaceTPSL(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanPlaceTrailStop(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanPlacePositionsTPSL(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanModifyTPSLPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanCancelPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanCancelSymbolPlan(params?: {}): Promise<implicitReturnType>;
    privatePostApiMixV1PlanCancelAllPlan(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
