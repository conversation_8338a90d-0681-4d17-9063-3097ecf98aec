'use strict';

// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

var FunctionAbiType;
(function (FunctionAbiType) {
    FunctionAbiType[FunctionAbiType["function"] = 0] = "function";
    FunctionAbiType[FunctionAbiType["l1_handler"] = 1] = "l1_handler";
    FunctionAbiType[FunctionAbiType["constructor"] = 2] = "constructor";
})(FunctionAbiType || (FunctionAbiType = {}));
