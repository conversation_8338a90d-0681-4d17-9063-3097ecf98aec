'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./abi.js');

// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

// Basic elements
exports.EntryPointType = void 0;
(function (EntryPointType) {
    EntryPointType["EXTERNAL"] = "EXTERNAL";
    EntryPointType["L1_HANDLER"] = "L1_HANDLER";
    EntryPointType["CONSTRUCTOR"] = "CONSTRUCTOR";
})(exports.EntryPointType || (exports.EntryPointType = {}));
